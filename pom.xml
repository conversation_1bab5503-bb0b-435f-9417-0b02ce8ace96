<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.yxt</groupId>
    <artifactId>yxt-xframe2</artifactId>
    <version>2.0.1-SNAPSHOT</version>
    <relativePath/>
  </parent>

  <groupId>com.yxt.order</groupId>
  <artifactId>order-assistant</artifactId>
  <version>1.0.0</version>
  <packaging>pom</packaging>
  <modules>
    <module>order-assistant-server</module>
    <module>order-assistant-bootstrap</module>
    <module>order-assistant-knowledge</module>
    <module>order-assistant-prompt</module>
  </modules>

  <properties>
    <skipTests>true</skipTests> <!-- 跳过单元测试 -->
    <spring-boot.repackage.skip>false</spring-boot.repackage.skip>

    <java.version>21</java.version>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>

    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <easyexcel.version>3.3.3</easyexcel.version>
    <loop-cure.version>1.2.1-SNAPSHOT</loop-cure.version>
    <elasticsearch.version>7.14.0</elasticsearch.version>
    <poi.version>3.15</poi.version>
    <org.mapstruct>1.3.1.Final</org.mapstruct>
    <yxt.order-types.version>2.2.1-RELEASE</yxt.order-types.version>
    <yxt.order-common.version>2.2.1-RELEASE</yxt.order-common.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-core-spring-boot-starter2</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-common-alarm2</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${org.mapstruct}</version>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <version>${org.mapstruct}</version>
    </dependency>

    <dependency>
      <groupId>cn.hydee.starter</groupId>
      <artifactId>grey-spring-boot-lib</artifactId>
      <version>3.5.1-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- WebSocket -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>

    <!-- OpenFeign -->
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
      <exclusions>
        <!-- 排除 Ribbon 依赖 -->
        <exclusion>
          <groupId>org.springframework.cloud</groupId>
          <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Spring Cloud LoadBalancer (替代Ribbon) -->
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-loadbalancer</artifactId>
    </dependency>

    <!-- feign-okhttp -->
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-okhttp</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.squareup.okhttp3</groupId>
          <artifactId>okhttp</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-pool2</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
      <version>2.1</version>
    </dependency>

    <!-- gson -->
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.8.5</version>
    </dependency>

    <!-- Hutool工具类所有模块 -->
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
    </dependency>

    <!-- okhttp -->
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

  </dependencies>



  <repositories>
    <repository>
      <id>aliyun</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>

    <repository>
      <id>hydee</id>
      <name>hydee</name>
      <url>http://nexus.hxyxt.com/nexus/content/groups/public/</url>
    </repository>
    <repository>
      <id>local-snapshots</id>
      <name>local-snapshots</name>
      <url>https://nexus.hxyxt.com/repository/snapshots/</url>
    </repository>
    <repository>
      <id>local-releases</id>
      <name>local-releases</name>
      <url>https://nexus.hxyxt.com/repository/releases/</url>
    </repository>
  </repositories>

  <distributionManagement>
    <repository>
      <id>local-releases</id>
      <name>Nexus Release Repository</name>
      <url>https://nexus.hxyxt.com/repository/releases/</url>
    </repository>
    <snapshotRepository>
      <id>local-snapshots</id>
      <name>Nexus Snapshot Repository</name>
      <url>https://nexus.hxyxt.com/repository/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>



</project>
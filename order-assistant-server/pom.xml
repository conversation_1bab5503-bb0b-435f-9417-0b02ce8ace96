<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt.order</groupId>
    <artifactId>order-assistant</artifactId>
    <version>1.0.0</version>
  </parent>

  <groupId>com.yxt.order.assistant.server</groupId>
  <artifactId>order-assistant-server</artifactId>
  <packaging>jar</packaging>

  <properties>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    <hippo4j.version>1.4.3</hippo4j.version>
  </properties>

  <dependencies>

    <dependency>
      <groupId>io.github.imfangs</groupId>
      <artifactId>dify-java-client</artifactId>
      <version>1.1.3</version>
    </dependency>

    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-common-wechatrobot</artifactId>
      <version>4.10.3</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba.csp</groupId>
      <artifactId>sentinel-datasource-nacos</artifactId>
      <version>1.8.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents.client5</groupId>
      <artifactId>httpclient5</artifactId>
      <version>5.2.1</version>
    </dependency>
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.17.1</version>
    </dependency>
    <!--notey: 这里引入了 xframe 的版本,不是xframe2的版本;如果后期引入了xframe2要记得排查相关依赖-->
    <dependency>
      <groupId>com.yxt.order.atom.sdk</groupId>
      <artifactId>order-atom-sdk</artifactId>
      <version>order-assistant-SNAPSHOT</version>
    </dependency>

    <!-- 动态数据源 -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
    </dependency>
    <!-- MyBatis-Plus -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
    </dependency>

    <!-- MyBatis-Plus Generator -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
      <version>3.5.12</version>
    </dependency>

    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis-typehandlers-jsr310</artifactId>
      <version>1.0.2</version>
    </dependency>
    <!-- 数据源 -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-3-starter</artifactId>
    </dependency>

    <!-- 数据库驱动 -->
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
      <scope>runtime</scope>
    </dependency>


  </dependencies>


</project>
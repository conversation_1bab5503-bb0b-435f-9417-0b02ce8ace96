package com.yxt.order.assistant.server.knowledge.database;

import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseTableInfo;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase.DatabaseConfig;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 数据库模式查询服务 支持配置文件数据源和动态数据源两种模式
 */
@Slf4j
@Service
public class DatabaseSchemaService {



  @Resource
  private JdbcDatabaseSchemaService jdbcDatabaseSchemaService;

  /**
   * 根据请求参数获取表信息
   */
  public List<DatabaseTableInfo> getTablesInfo(KnowledgeBase req) {

    DatabaseConfig dbConfigFromJson = req.fetchDbConfigFromJson();
    // 如果提供了动态数据源配置，使用动态数据源
    if (CollectionUtils.isEmpty(dbConfigFromJson.getDataSourceList())) {
      throw new RuntimeException("数据源配置为空");
    }

    return jdbcDatabaseSchemaService.getTablesInfo(dbConfigFromJson.getDataSourceList());

  }


}

package com.yxt.order.assistant.server.knowledge.database;

import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseTableInfo;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase.DataSourceConfig;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 基于JDBC的数据库模式查询服务
 */
@Slf4j
@Service
public class JdbcDatabaseSchemaService {

  @Resource
  private JdbcConnectionManager connectionManager;


  /**
   * 获取多个数据源的表信息
   */
  public List<DatabaseTableInfo> getTablesInfo(List<DataSourceConfig> dataSourceConfigs) {
    List<DatabaseTableInfo> allTables = new ArrayList<>();

    if (dataSourceConfigs == null || dataSourceConfigs.isEmpty()) {
      log.warn("数据源配置列表为空");
      return allTables;
    }

    // 验证数据源配置
    List<DataSourceConfig> validConfigs = dataSourceConfigs.stream().filter(config -> {
      if (!config.isValid()) {
        log.warn("数据源配置无效: {}", config.getName());
        return false;
      }
      return true;
    }).collect(Collectors.toList());

    if (validConfigs.isEmpty()) {
      log.warn("没有有效的数据源配置");
      throw new RuntimeException("没有有效的数据源配置");
    }

    // 串行处理
    for (DataSourceConfig config : validConfigs) {
      try {
        List<DatabaseTableInfo> tables = getTablesInfoFromSingleDataSource(config);
        allTables.addAll(tables);
      } catch (Exception e) {
        log.error("获取数据源 {} 的表信息失败", config.getName(), e);
      }
    }

    log.info("共获取到 {} 张表的信息", allTables.size());
    return allTables;
  }

  /**
   * 获取单个数据源的表信息
   */
  public List<DatabaseTableInfo> getTablesInfoFromSingleDataSource(DataSourceConfig config) {
    List<DatabaseTableInfo> tables = new ArrayList<>();

    log.info("开始获取数据源 {} 的表信息", config.getName());

    // 测试连接
    if (config.getTestConnection() && !connectionManager.testConnection(config)) {
      log.error("数据源 {} 连接测试失败", config.getName());
      return tables;
    }

    try (Connection connection = connectionManager.getConnection(config)) {
      String schemaName = config.getDatabaseName();

      // 查询表基本信息
      String tableQuery = buildTableQuery();

      try (PreparedStatement stmt = connection.prepareStatement(tableQuery)) {
        stmt.setString(1, schemaName);

        try (ResultSet rs = stmt.executeQuery()) {
          while (rs.next()) {
            DatabaseTableInfo tableInfo = new DatabaseTableInfo();
            tableInfo.setDataSourceName(config.getName());
            tableInfo.setSchemaName(schemaName);
            tableInfo.setTableName(rs.getString("TABLE_NAME"));


            // 获取原始创建表语句
            String originSql = getCreateTableSql(connection, tableInfo.getTableName());
            tableInfo.setOriginSql(originSql);

            tables.add(tableInfo);
          }
        }
      }

      log.info("数据源 {} 共获取到 {} 张表", config.getName(), tables.size());

    } catch (SQLException e) {
      log.error("获取数据源 {} 的表信息失败", config.getName(), e);
      throw new RuntimeException("获取数据源表信息失败: " + config.getName(), e);
    }

    return tables;
  }

  /**
   * 构建表查询SQL
   */
  private String buildTableQuery() {
    StringBuilder sql = new StringBuilder();
    sql.append("SELECT ");
    sql.append("TABLE_NAME, ");
    sql.append("TABLE_COMMENT, ");
    sql.append("TABLE_TYPE, ");
    sql.append("ENGINE, ");
    sql.append("TABLE_COLLATION, ");
    sql.append("CREATE_TIME, ");
    sql.append("UPDATE_TIME, ");
    sql.append("TABLE_ROWS, ");
    sql.append("DATA_LENGTH, ");
    sql.append("INDEX_LENGTH ");
    sql.append("FROM information_schema.TABLES ");
    sql.append("WHERE TABLE_SCHEMA = ? ");
    sql.append("AND TABLE_TYPE = 'BASE TABLE' ");
    sql.append("ORDER BY TABLE_NAME");
    return sql.toString();
  }


  /**
   * 获取表的创建语句
   */
  private String getCreateTableSql(Connection connection, String tableName) {
    String createTableSql = null;
    String query = "SHOW CREATE TABLE " + tableName;

    try (PreparedStatement stmt = connection.prepareStatement(query)) {
      try (ResultSet rs = stmt.executeQuery()) {
        if (rs.next()) {
          createTableSql = rs.getString(2); // 第二列是Create Table语句
        }
      }
    } catch (SQLException e) {
      log.warn("获取表 {} 的创建语句失败: {}", tableName, e.getMessage());
      // 如果获取失败，返回null而不是抛出异常，保证其他信息仍能正常获取
    }

    return createTableSql;
  }


}

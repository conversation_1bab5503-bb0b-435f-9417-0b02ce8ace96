package com.yxt.order.assistant.server.dify;


import com.yxt.order.assistant.server.config.OrderAssistantConfig;
import com.yxt.order.assistant.server.config.OrderAssistantConfig.Dify;
import io.github.imfangs.dify.client.DifyClientFactory;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import javax.annotation.Resource;
import org.apache.hc.core5.util.Asserts;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

@Component
public class DifyComponent {

  @Resource
  private OrderAssistantConfig orderAssistantConfig;

  @Bean
  public DifyDatasetsClient difyDatasetsClient() {
    Dify dify = orderAssistantConfig.getDify();
    Assert.notNull(dify, "请配置dify相关配置");
    String apiKey = dify.getApiKey();
    String baseUrl = dify.getBaseUrl();
    Asserts.notBlank(baseUrl, "dify baseUrl未配置");
    Asserts.notBlank(apiKey, "dify apiKey未配置");

    return DifyClientFactory.createDatasetsClient(baseUrl, apiKey);
  }

}

package com.yxt.order.assistant.server.knowledge;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.assistant.server.knowledge.req.CreateKnowledgeBaseReq;
import com.yxt.order.assistant.server.knowledge.req.KnowledgeDetailReq;
import com.yxt.order.assistant.server.knowledge.req.DeleteKnowledgeBaseDocumentReq;
import com.yxt.order.assistant.server.knowledge.req.UploadKnowledgeBaseDocumentReq;
import com.yxt.order.assistant.server.knowledge.service.KnowledgeService;
import com.yxt.order.assistant.server.repository.KnowledgeBaseRepository;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase;
import com.yxt.starter.controller.AbstractController;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.exception.DifyApiException;
import io.github.imfangs.dify.client.model.datasets.DatasetResponse;
import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/knowledge/data-set")
public class KnowledgeBaseController extends AbstractController {

  @Resource
  private DifyDatasetsClient difyDatasetsClient;

  @Resource
  private KnowledgeService knowledgeService;

  @Resource
  private KnowledgeBaseRepository knowledgeBaseRepository;

  /**
   * 创建知识库
   */
  @PostMapping("/create")
  public ResponseBase<Boolean> create(@RequestBody CreateKnowledgeBaseReq req) {
    knowledgeService.create(req);
    return generateSuccess(Boolean.TRUE);
  }



  /**
   * 获取所有支持库(订单助手自有的)
   *
   * @return
   */
  @PostMapping("/list-all-knowledge")
  public ResponseBase<List<KnowledgeBase>> listAllKnowledge() {
    LambdaQueryWrapper<KnowledgeBase> query = new LambdaQueryWrapper<>();
    return generateSuccess(knowledgeBaseRepository.selectList(query));
  }

  /**
   * 获取知识库详情
   *
   * @param req
   * @return
   * @throws DifyApiException
   * @throws IOException
   */
  @PostMapping("/detail")
  public ResponseBase<DatasetResponse> detail(@RequestBody KnowledgeDetailReq req)
      throws DifyApiException, IOException {
    DatasetResponse dataset = difyDatasetsClient.getDataset(req.getDataSetId());
    return generateSuccess(dataset);
  }


  /**
   * 上传文档到知识库
   *
   * @param req
   * @return
   * @throws DifyApiException
   * @throws IOException
   */
  @PostMapping("/upload")
  public ResponseBase<Boolean> upload(@RequestBody UploadKnowledgeBaseDocumentReq req) {
    knowledgeService.upload(req);
    return generateSuccess(Boolean.TRUE);
  }

  /**
   * 删除知识库所有文档
   */
  @PostMapping("/delete")
  public ResponseBase<Boolean> delete(@RequestBody DeleteKnowledgeBaseDocumentReq req) {
    knowledgeService.delete(req);
    return generateSuccess(Boolean.TRUE);
  }

}

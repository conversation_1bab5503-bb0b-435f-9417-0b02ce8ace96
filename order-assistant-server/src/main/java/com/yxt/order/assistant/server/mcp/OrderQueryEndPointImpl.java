package com.yxt.order.assistant.server.mcp;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.assistant.server.mcp.feign.OrderMcpApiFeign;
import com.yxt.order.atom.sdk.mcp.req.OrderTypeQueryReq;
import com.yxt.order.atom.sdk.mcp.res.OrderTypeQueryRes;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class OrderQueryEndPointImpl implements OrderQueryEndPoint {

  @Resource
  private OrderMcpApiFeign orderMcpApiFeign;

  @Override
  public ResponseBase<OrderTypeQueryRes> queryOrderType(OrderTypeQueryReq orderTypeReq) {
    return orderMcpApiFeign.orderType(orderTypeReq);
  }


}

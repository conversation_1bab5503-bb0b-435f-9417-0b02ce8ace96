package com.yxt.order.assistant.server.knowledge.req;

import com.yxt.order.assistant.server.repository.enums.KnowledgeBaseSource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建知识库
 */
@Data
@ApiModel("创建知识库请求参数")
public class CreateKnowledgeBaseReq {

  @ApiModelProperty("知识库名称")
  @NotEmpty
  private String name;

  /**
   * 知识库描述
   */
  private String description;


  @ApiModelProperty("知识库来源")
  @NotNull
  private KnowledgeBaseSource source;

}

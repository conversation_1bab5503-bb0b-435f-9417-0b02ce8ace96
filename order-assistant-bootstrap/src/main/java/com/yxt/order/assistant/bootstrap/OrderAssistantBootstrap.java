package com.yxt.order.assistant.bootstrap;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;


@MapperScan(value = {"com.yxt.order.assistant.server.repository"})
@SpringBootApplication
@EnableFeignClients(basePackages = {"com.yxt.order"})
@ComponentScan(basePackages = {"com.yxt.order", "cn.hydee.starter.grey"})
@Slf4j
@EnableRetry
@EnableAsync
public class OrderAssistantBootstrap {
  public static void main(String[] args) {
    SpringApplication.run(OrderAssistantBootstrap.class, args);
    log.info("OrderAssistantBootstrap服务启动成功");
  }
}